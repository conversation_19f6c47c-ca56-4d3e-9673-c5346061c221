{"name": "mixvideo", "version": "0.1.1", "description": "Multi-language Tauri desktop application with gRPC communication", "private": true, "type": "module", "scripts": {"dev": "pnpm run --parallel dev", "build": "pnpm run --recursive build", "test": "pnpm run --recursive test", "lint": "pnpm run --recursive lint", "format": "pnpm run --recursive format", "clean": "pnpm run --recursive clean", "setup": "./tools/scripts/setup.sh", "proto:generate": "./packages/shared/scripts/generate-proto.sh", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "tauri:dev": "pnpm --filter=@mixvideo/desktop tauri:dev", "tauri:build": "pnpm --filter=@mixvideo/desktop tauri:build", "python:dev": "pnpm --filter python-service dev", "services:start": "docker-compose up python-service -d"}, "devDependencies": {"@types/node": "^24.0.13", "concurrently": "^8.2.2", "eslint": "^8.57.1", "prettier": "^3.6.2", "tsup": "^8.5.0", "tsx": "^4.20.3", "typescript": "^5.8.3"}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "packageManager": "pnpm@8.15.0", "keywords": ["tauri", "desktop", "grpc", "rust", "python", "typescript", "multi-language"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "dependencies": {"@types/react-window": "^1.8.8", "react-window": "^1.8.11"}}