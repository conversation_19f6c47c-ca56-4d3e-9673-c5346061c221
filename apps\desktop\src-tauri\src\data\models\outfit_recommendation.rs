use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

/// 色彩信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ColorInfo {
    /// 色彩名称
    pub name: String,
    /// 十六进制色值
    pub hex: String,
    /// HSV值 (0-1范围)
    pub hsv: (f32, f32, f32),
}

/// 穿搭单品
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OutfitItem {
    /// 单品类别 (如: "上装", "下装", "鞋子", "配饰")
    pub category: String,
    /// 单品描述
    pub description: String,
    /// 主要颜色
    pub primary_color: ColorInfo,
    /// 次要颜色 (可选)
    pub secondary_color: Option<ColorInfo>,
    /// 材质
    pub material: String,
    /// 风格标签
    pub style_tags: Vec<String>,
}

/// 场景建议
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SceneRecommendation {
    /// 场景名称
    pub name: String,
    /// 场景描述
    pub description: String,
    /// 场景类型 ("室内", "室外", "特殊")
    pub scene_type: String,
    /// 适合的时间段
    pub time_of_day: Vec<String>,
    /// 光线条件
    pub lighting: String,
    /// 拍摄建议
    pub photography_tips: Vec<String>,
}

/// 穿搭方案
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OutfitRecommendation {
    /// 方案ID
    pub id: String,
    /// 方案标题
    pub title: String,
    /// 方案描述
    pub description: String,
    /// 整体风格
    pub overall_style: String,
    /// 风格标签
    pub style_tags: Vec<String>,
    /// 适合场合
    pub occasions: Vec<String>,
    /// 适合季节
    pub seasons: Vec<String>,
    /// 穿搭单品列表
    pub items: Vec<OutfitItem>,
    /// 色彩搭配主题
    pub color_theme: String,
    /// 主要色彩
    pub primary_colors: Vec<ColorInfo>,
    /// 场景建议
    pub scene_recommendations: Vec<SceneRecommendation>,
    /// TikTok优化建议
    pub tiktok_tips: Vec<String>,
    /// 搭配要点
    pub styling_tips: Vec<String>,
    /// 创建时间
    pub created_at: DateTime<Utc>,
}

/// 穿搭方案生成请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OutfitRecommendationRequest {
    /// 用户输入的关键词或描述
    pub query: String,
    /// 目标风格 (可选)
    pub target_style: Option<String>,
    /// 适合场合 (可选)
    pub occasions: Option<Vec<String>>,
    /// 季节偏好 (可选)
    pub season: Option<String>,
    /// 色彩偏好 (可选)
    pub color_preferences: Option<Vec<String>>,
    /// 生成数量
    pub count: u32,
}

/// 穿搭方案生成响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OutfitRecommendationResponse {
    /// 生成的穿搭方案列表
    pub recommendations: Vec<OutfitRecommendation>,
    /// 生成时间 (毫秒)
    pub generation_time_ms: u64,
    /// 生成时间戳
    pub generated_at: DateTime<Utc>,
    /// 使用的提示词 (调试用)
    pub prompt_used: Option<String>,
}

/// 场景检索请求 (用于方案详情到场景检索的集成)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SceneSearchRequest {
    /// 穿搭方案ID
    pub outfit_id: String,
    /// 场景描述
    pub scene_description: String,
    /// 风格标签
    pub style_tags: Vec<String>,
    /// 色彩信息
    pub colors: Vec<ColorInfo>,
    /// 搜索参数
    pub search_params: SceneSearchParams,
}

/// 场景检索参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SceneSearchParams {
    /// 场景类型过滤
    pub scene_types: Option<Vec<String>>,
    /// 时间段过滤
    pub time_filters: Option<Vec<String>>,
    /// 光线条件过滤
    pub lighting_filters: Option<Vec<String>>,
    /// 结果数量限制
    pub limit: Option<u32>,
}

impl Default for OutfitRecommendationRequest {
    fn default() -> Self {
        Self {
            query: String::new(),
            target_style: None,
            occasions: None,
            season: None,
            color_preferences: None,
            count: 3, // 默认生成3个方案
        }
    }
}

impl OutfitRecommendation {
    /// 生成用于场景检索的描述
    pub fn generate_scene_search_description(&self) -> String {
        let mut description = format!("{} - {}", self.title, self.description);
        
        if !self.style_tags.is_empty() {
            description.push_str(&format!(" 风格: {}", self.style_tags.join(", ")));
        }
        
        if !self.primary_colors.is_empty() {
            let color_names: Vec<String> = self.primary_colors.iter()
                .map(|c| c.name.clone())
                .collect();
            description.push_str(&format!(" 主要色彩: {}", color_names.join(", ")));
        }
        
        description
    }
    
    /// 获取所有风格标签 (包括整体风格和单品风格)
    pub fn get_all_style_tags(&self) -> Vec<String> {
        let mut all_tags = self.style_tags.clone();
        
        // 添加整体风格
        if !self.overall_style.is_empty() {
            all_tags.push(self.overall_style.clone());
        }
        
        // 添加单品风格标签
        for item in &self.items {
            all_tags.extend(item.style_tags.clone());
        }
        
        // 去重并排序
        all_tags.sort();
        all_tags.dedup();
        all_tags
    }
}
