/**
 * 聊天功能测试工具
 * 用于验证 RAG Grounding 服务和聊天界面是否正常工作
 */

import { queryRagGrounding, testRagGroundingConnection, getRagGroundingConfig } from '../services/ragGroundingService';

/**
 * 控制台日志工具
 */
export class ConsoleLogger {
  static logChatResponse(userInput: string, response: any, totalTime: number) {
    console.group('🎯 聊天回复详情');
    console.log('📝 用户输入:', userInput);
    console.log('💬 AI 回答:', response.answer);
    console.log('⏱️ 后端响应时间:', response.response_time_ms + 'ms');
    console.log('🕐 前端总耗时:', totalTime + 'ms');
    console.log('🤖 使用模型:', response.model_used);
    console.log('🕐 时间戳:', new Date().toLocaleString());

    if (response.grounding_metadata?.sources && response.grounding_metadata.sources.length > 0) {
      console.log('📚 参考来源 (' + response.grounding_metadata.sources.length + ' 条):');
      response.grounding_metadata.sources.forEach((source: any, index: number) => {
        console.log(`  📖 ${index + 1}. ${source.title}`);
        if (source.content) {
          console.log(`     📄 内容: ${JSON.stringify(source.content, null, 2)}`);
        }
        if (source.uri) {
          console.log(`     🔗 链接: ${source.uri}`);
        }
      });
    }

    if (response.grounding_metadata?.search_queries && response.grounding_metadata.search_queries.length > 0) {
      console.log('🔍 搜索查询:', response.grounding_metadata.search_queries);
    }

    console.groupEnd();
  }

  static logError(operation: string, error: any) {
    console.group('❌ 错误详情');
    console.log('🔧 操作:', operation);
    console.log('💥 错误信息:', error);
    console.log('🕐 时间戳:', new Date().toLocaleString());
    console.groupEnd();
  }

  static logSuccess(operation: string, details?: any) {
    console.group('✅ 操作成功');
    console.log('🔧 操作:', operation);
    if (details) {
      console.log('📊 详情:', details);
    }
    console.log('🕐 时间戳:', new Date().toLocaleString());
    console.groupEnd();
  }
}

/**
 * 测试 RAG Grounding 连接
 */
export async function testConnection(): Promise<boolean> {
  try {
    console.log('🔧 测试 RAG Grounding 连接...');
    const result = await testRagGroundingConnection();
    console.log('✅ 连接测试成功:', result);
    return true;
  } catch (error) {
    console.error('❌ 连接测试失败:', error);
    return false;
  }
}

/**
 * 测试配置获取
 */
export async function testConfig(): Promise<boolean> {
  try {
    console.log('📋 获取 RAG Grounding 配置...');
    const config = await getRagGroundingConfig();
    console.log('✅ 配置获取成功:', config);
    return true;
  } catch (error) {
    console.error('❌ 配置获取失败:', error);
    return false;
  }
}

/**
 * 测试简单查询
 */
export async function testSimpleQuery(): Promise<boolean> {
  try {
    const result = await queryRagGrounding('你好，请介绍一下自己', {
      sessionId: 'test-session',
      includeMetadata: true,
      timeout: 30000
    });

    if (result.success && result.data) {
      return true;
    } else {
      console.error('❌ 查询失败:', result.error);
      return false;
    }
  } catch (error) {
    console.error('❌ 查询异常:', error);
    return false;
  }
}

/**
 * 测试上下文保持
 */
export async function testContextRetention(): Promise<boolean> {
  try {
    console.log('🔄 测试上下文保持...');
    const sessionId = `test-context-${Date.now()}`;

    // 第一个问题
    const result1 = await queryRagGrounding('我的名字是张三', {
      sessionId,
      includeMetadata: false,
      timeout: 30000
    });

    if (!result1.success) {
      console.error('❌ 第一个查询失败:', result1.error);
      return false;
    }

    // 等待一秒
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 第二个问题，测试是否记住了名字
    const result2 = await queryRagGrounding('你还记得我的名字吗？', {
      sessionId,
      includeMetadata: false,
      timeout: 30000
    });

    if (result2.success && result2.data) {
      const contextRetained = result2.data.answer.includes('张三') || result2.data.answer.includes('名字');

      console.group('✅ 上下文保持测试结果');
      console.log('👤 会话ID:', sessionId);
      console.log('📝 第一个问题: "我的名字是张三"');
      console.log('💬 第一个回答:', result1.data?.answer.substring(0, 150) + '...');
      console.log('📝 第二个问题: "你还记得我的名字吗？"');
      console.log('💬 第二个回答:', result2.data.answer);
      console.log('🧠 上下文保持:', contextRetained ? '✅ 成功' : '❌ 失败');
      console.log('⏱️ 第一次响应时间:', result1.data?.response_time_ms + 'ms');
      console.log('⏱️ 第二次响应时间:', result2.data.response_time_ms + 'ms');

      if (contextRetained) {
        console.log('🎉 AI 成功记住了用户的名字！');
      } else {
        console.warn('⚠️ AI 没有记住用户的名字，上下文可能丢失');
      }
      console.groupEnd();

      return true;
    } else {
      console.error('❌ 第二个查询失败:', result2.error);
      return false;
    }
  } catch (error) {
    console.error('❌ 上下文测试异常:', error);
    return false;
  }
}

/**
 * 运行所有测试
 */
export async function runAllTests(): Promise<void> {
  console.log('🚀 开始运行聊天功能测试...');
  
  const tests = [
    { name: '连接测试', fn: testConnection },
    { name: '配置测试', fn: testConfig },
    { name: '简单查询测试', fn: testSimpleQuery },
    { name: '上下文保持测试', fn: testContextRetention }
  ];

  const results: { name: string; success: boolean }[] = [];

  for (const test of tests) {
    console.log(`\n--- ${test.name} ---`);
    try {
      const success = await test.fn();
      results.push({ name: test.name, success });
    } catch (error) {
      console.error(`${test.name} 执行异常:`, error);
      results.push({ name: test.name, success: false });
    }
  }

  // 输出测试结果摘要
  console.log('\n📊 测试结果摘要:');
  results.forEach(result => {
    console.log(`${result.success ? '✅' : '❌'} ${result.name}: ${result.success ? '通过' : '失败'}`);
  });

  const passedTests = results.filter(r => r.success).length;
  const totalTests = results.length;
  console.log(`\n🎯 总体结果: ${passedTests}/${totalTests} 测试通过`);

  if (passedTests === totalTests) {
    console.log('🎉 所有测试通过！聊天功能正常工作。');
  } else {
    console.log('⚠️ 部分测试失败，请检查相关配置和服务状态。');
  }
}

// 在浏览器控制台中可以直接调用的测试函数
if (typeof window !== 'undefined') {
  (window as any).testChatFunction = {
    testConnection,
    testConfig,
    testSimpleQuery,
    testContextRetention,
    runAllTests
  };
  
  console.log('🔧 聊天功能测试工具已加载，可在控制台中使用:');
  console.log('- window.testChatFunction.testConnection()');
  console.log('- window.testChatFunction.testConfig()');
  console.log('- window.testChatFunction.testSimpleQuery()');
  console.log('- window.testChatFunction.testContextRetention()');
  console.log('- window.testChatFunction.runAllTests()');
}
