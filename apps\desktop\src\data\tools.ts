import {
  FileText,
  Code,
  Bug,
  Wrench,
  Database,
  FileSearch,
  MessageCircle,
  Droplets,
  ImageIcon,
  Search,
  Sparkles,
  Filter,
  BarChart3
} from 'lucide-react';
import { Tool, ToolCategory, ToolStatus } from '../types/tool';

/**
 * 工具数据配置
 * 定义所有可用的工具及其属性
 */
export const TOOLS_DATA: Tool[] = [
  {
    id: 'data-cleaning',
    name: 'AI检索图片/数据清洗',
    description: 'JSONL格式数据去重处理工具，支持基于URI字段的精确匹配去重',
    longDescription: '专业的数据清洗工具，支持JSONL格式文件的批量处理。通过URI字段进行精确匹配，快速去除重复数据，提供实时进度显示和详细的处理统计信息。',
    icon: FileText,
    route: '/tools/data-cleaning',
    category: ToolCategory.DATA_PROCESSING,
    status: ToolStatus.STABLE,
    tags: ['JSONL', '数据去重', '批量处理', 'URI匹配'],
    isPopular: true,
    version: '1.0.0',
    lastUpdated: '2024-01-15'
  },
  {
    id: 'json-parser',
    name: '容错JSON解析器',
    description: '基于Tree-sitter的大模型JSON容错解析器，支持处理不规范的JSON数据',
    longDescription: '高性能的JSON解析工具，专门处理大模型返回的不规范JSON数据。支持注释、无引号键名、尾随逗号等非标准格式，提供多种错误恢复策略。',
    icon: Code,
    route: '/tools/json-parser',
    category: ToolCategory.DEVELOPMENT,
    status: ToolStatus.STABLE,
    tags: ['JSON解析', 'Tree-sitter', '容错处理', '大模型'],
    isNew: true,
    version: '2.1.0',
    lastUpdated: '2024-01-20'
  },
  {
    id: 'debug-panel',
    name: 'JSON解析器调试面板',
    description: '用于测试后端命令是否正常工作的调试工具面板',
    longDescription: '开发者调试工具，提供完整的后端命令测试功能。支持实时测试各种JSON解析场景，查看详细的错误信息和性能统计数据。',
    icon: Bug,
    route: '/tools/debug-panel',
    category: ToolCategory.DEVELOPMENT,
    status: ToolStatus.BETA,
    tags: ['调试工具', '后端测试', '命令测试', '开发辅助'],
    version: '1.2.0',
    lastUpdated: '2024-01-18'
  },
  {
    id: 'ai-chat',
    name: 'AI 智能聊天',
    description: '基于 RAG 检索增强生成的智能对话助手，支持上下文保持和知识问答',
    longDescription: '先进的AI聊天工具，基于RAG（检索增强生成）技术，能够根据知识库提供准确的答案。支持上下文保持，最多保留3条对话记录，实时显示响应时间和参考来源。',
    icon: MessageCircle,
    route: '/tools/ai-chat',
    category: ToolCategory.AI_TOOLS,
    status: ToolStatus.STABLE,
    tags: ['AI聊天', 'RAG', '知识问答', '智能助手', '上下文保持'],
    isNew: true,
    isPopular: true,
    version: '1.0.0',
    lastUpdated: '2024-01-21'
  },
  {
    id: 'watermark-tool',
    name: '水印处理工具',
    description: '专业的视频水印检测、移除和添加工具，支持批量处理和多种水印类型',
    longDescription: '强大的水印处理工具集，提供智能水印检测、精确移除和自定义添加功能。支持视频和图片格式，提供多种移除算法（AI修复、模糊处理、裁剪等）和丰富的水印样式选择。',
    icon: Droplets,
    route: '/tools/watermark',
    category: ToolCategory.FILE_PROCESSING,
    status: ToolStatus.STABLE,
    tags: ['水印检测', '水印移除', '水印添加', '批量处理', '视频处理'],
    isNew: true,
    isPopular: true,
    version: '1.0.0',
    lastUpdated: '2024-01-23'
  },
  {
    id: 'batch-thumbnail-generator',
    name: '批量缩略图生成器',
    description: '为视频文件批量生成预览缩略图和时间轴，支持自定义时间戳、尺寸和格式',
    longDescription: '专业的批量缩略图生成工具，支持多种视频格式的批量处理。提供灵活的时间戳配置、多种尺寸预设、智能场景检测和时间轴缩略图生成功能。支持并发处理、进度监控和错误恢复机制。',
    icon: ImageIcon,
    route: '/tools/batch-thumbnail-generator',
    category: ToolCategory.FILE_PROCESSING,
    status: ToolStatus.STABLE,
    tags: ['缩略图生成', '批量处理', '视频处理', '时间轴', '场景检测'],
    isNew: true,
    isPopular: true,
    version: '1.0.0',
    lastUpdated: '2024-01-24'
  },
  {
    id: 'similarity-search',
    name: '相似度检索工具',
    description: '基于AI的智能相似度搜索工具，支持多种相关性阈值和快速搜索功能',
    longDescription: '强大的AI驱动相似度检索工具，基于先进的机器学习算法提供精准的内容匹配。支持可调节的相关性阈值、智能搜索建议、实时结果展示和批量处理功能。适用于图像、文本和多媒体内容的相似性分析。',
    icon: Search,
    route: '/tools/similarity-search',
    category: ToolCategory.AI_TOOLS,
    status: ToolStatus.STABLE,
    tags: ['AI搜索', '相似度检索', '智能匹配', '机器学习', '内容分析'],
    isNew: true,
    isPopular: true,
    version: '1.0.0',
    lastUpdated: '2024-01-25'
  },
  {
    id: 'outfit-recommendation',
    name: 'AI穿搭方案推荐',
    description: '基于TikTok视觉趋势的智能穿搭建议工具，提供个性化的时尚搭配方案',
    longDescription: '专业的AI穿搭顾问工具，基于TikTok视觉趋势和时尚潮流，为用户生成个性化的穿搭方案。支持多种风格选择、场合匹配、色彩搭配建议，并提供TikTok优化建议和拍摄技巧，助力内容创作和时尚搭配。',
    icon: Sparkles,
    route: '/tools/outfit-recommendation',
    category: ToolCategory.AI_TOOLS,
    status: ToolStatus.STABLE,
    tags: ['AI穿搭', '时尚搭配', 'TikTok', '个性化推荐', '视觉趋势'],
    isNew: true,
    isPopular: true,
    version: '1.0.0',
    lastUpdated: '2024-01-25'
  },
  {
    id: 'advanced-filter-demo',
    name: '高级过滤器演示',
    description: '展示和测试高级过滤器组件功能，包括类别、环境、设计风格和颜色检测',
    longDescription: '专业的高级过滤器演示工具，展示完整的过滤器组件功能。包括类别过滤器、环境标签选择器、设计风格选择器和颜色检测过滤器。支持实时配置预览、JSON导出和过滤器摘要显示，是开发和测试过滤器功能的理想工具。',
    icon: Filter,
    route: '/tools/advanced-filter-demo',
    category: ToolCategory.DEVELOPMENT,
    status: ToolStatus.BETA,
    tags: ['过滤器', '组件演示', '开发工具', 'UI组件', '配置管理'],
    isNew: true,
    version: '1.0.0',
    lastUpdated: '2024-01-25'
  },
  {
    id: 'outfit-search',
    name: '智能服装搜索',
    description: '基于AI的智能服装搜索工具，支持图像解析、相似度搜索和LLM问答',
    longDescription: '专业的服装搜索工具，基于先进的AI技术提供智能服装匹配和搜索功能。支持图像上传解析、多维度过滤搜索、相似度匹配、LLM智能问答等功能。提供直观的双列布局界面，左侧展示搜索结果，右侧提供搜索控制面板。',
    icon: Search,
    route: '/tools/outfit-search',
    category: ToolCategory.AI_TOOLS,
    status: ToolStatus.BETA,
    tags: ['AI搜索', '服装匹配', '图像解析', 'LLM问答', '相似度搜索'],
    isNew: true,
    isPopular: true,
    version: '1.0.0',
    lastUpdated: '2024-01-26'
  },
  {
    id: 'enriched-analysis-demo',
    name: '🎨 丰富图像分析演示',
    description: '展示智能服装分析的详细结果，包含颜色分析、风格分析、产品分析等丰富信息',
    longDescription: '专业的图像分析结果展示工具，提供极其详细和丰富的分析信息。包含颜色和谐度分析、色彩温度匹配、风格一致性评估、产品匹配度统计、环境适配性分析、拍摄质量评估、个性化搭配建议等功能。支持可展开的分析报告、统计图表、建议卡片和完整的分析摘要。',
    icon: BarChart3,
    route: '/tools/enriched-analysis-demo',
    category: ToolCategory.AI_TOOLS,
    status: ToolStatus.BETA,
    tags: ['图像分析', '详细报告', '颜色分析', '风格分析', '搭配建议', '统计图表'],
    isNew: true,
    isPopular: true,
    version: '1.0.0',
    lastUpdated: '2024-01-26'
  }
];

/**
 * 根据ID获取工具信息
 */
export const getToolById = (id: string): Tool | undefined => {
  return TOOLS_DATA.find(tool => tool.id === id);
};

/**
 * 根据分类获取工具列表
 */
export const getToolsByCategory = (category: ToolCategory): Tool[] => {
  return TOOLS_DATA.filter(tool => tool.category === category);
};

/**
 * 获取热门工具列表
 */
export const getPopularTools = (): Tool[] => {
  return TOOLS_DATA.filter(tool => tool.isPopular);
};

/**
 * 获取新功能工具列表
 */
export const getNewTools = (): Tool[] => {
  return TOOLS_DATA.filter(tool => tool.isNew);
};

/**
 * 搜索工具
 */
export const searchTools = (query: string): Tool[] => {
  const lowercaseQuery = query.toLowerCase();
  return TOOLS_DATA.filter(tool => 
    tool.name.toLowerCase().includes(lowercaseQuery) ||
    tool.description.toLowerCase().includes(lowercaseQuery) ||
    tool.tags?.some(tag => tag.toLowerCase().includes(lowercaseQuery))
  );
};

/**
 * 工具分类配置
 */
export const TOOL_CATEGORIES = [
  {
    id: ToolCategory.DATA_PROCESSING,
    name: '数据处理',
    description: '数据清洗、转换和处理工具',
    icon: Database,
    color: 'purple'
  },
  {
    id: ToolCategory.DEVELOPMENT,
    name: '开发调试',
    description: '开发和调试相关工具',
    icon: Code,
    color: 'indigo'
  },
  {
    id: ToolCategory.FILE_PROCESSING,
    name: '文件处理',
    description: '文件操作和处理工具',
    icon: FileSearch,
    color: 'orange'
  },
  {
    id: ToolCategory.AI_TOOLS,
    name: 'AI工具',
    description: '人工智能相关工具',
    icon: Wrench,
    color: 'pink'
  },
  {
    id: ToolCategory.UTILITIES,
    name: '实用工具',
    description: '通用实用工具集合',
    icon: Wrench,
    color: 'teal'
  }
];

/**
 * 获取分类配置
 */
export const getCategoryConfig = (category: ToolCategory) => {
  return TOOL_CATEGORIES.find(cat => cat.id === category);
};
